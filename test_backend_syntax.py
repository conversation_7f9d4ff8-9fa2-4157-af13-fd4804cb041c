#!/usr/bin/env python3
"""
Test script to validate backend syntax and basic functionality
"""
import sys
import os
import importlib.util

def test_syntax():
    """Test if main.py has valid syntax"""
    try:
        # Add backend to path
        backend_path = os.path.join(os.getcwd(), 'backend')
        if backend_path not in sys.path:
            sys.path.insert(0, backend_path)
        
        # Try to import main module
        spec = importlib.util.spec_from_file_location("main", "backend/main.py")
        main_module = importlib.util.module_from_spec(spec)
        
        print("✅ Syntax validation: PASSED")
        print("✅ Main module can be imported successfully")
        
        # Try to load the module
        spec.loader.exec_module(main_module)
        print("✅ Module execution: PASSED")
        
        # Check if FastAPI app exists
        if hasattr(main_module, 'app'):
            print("✅ FastAPI app found")
            
            # Check if key endpoints exist
            routes = [route.path for route in main_module.app.routes]
            key_endpoints = [
                "/api/health",
                "/api/alerts/active", 
                "/api/alerts/{alert_id}/acknowledge",
                "/api/alerts/{alert_id}/execute-action"
            ]
            
            for endpoint in key_endpoints:
                if any(endpoint in route for route in routes):
                    print(f"✅ Endpoint {endpoint} found")
                else:
                    print(f"⚠️ Endpoint {endpoint} not found")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax Error: {e}")
        print(f"   File: {e.filename}")
        print(f"   Line: {e.lineno}")
        print(f"   Text: {e.text}")
        return False
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
        
    except Exception as e:
        print(f"❌ General Error: {e}")
        return False

def test_alert_system_imports():
    """Test if alert system components can be imported"""
    try:
        # Test enhanced alert system
        try:
            from backend.enhanced_alert_system import enhanced_alert_system
            print("✅ Enhanced alert system imported successfully")
        except ImportError as e:
            print(f"⚠️ Enhanced alert system import failed: {e}")
        
        # Test alert actions
        try:
            from backend.alert_actions import alert_action_handler
            print("✅ Alert action handler imported successfully")
        except ImportError as e:
            print(f"⚠️ Alert action handler import failed: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ Alert system test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Hospital Agent Backend...")
    print("=" * 50)
    
    # Test syntax
    syntax_ok = test_syntax()
    print()
    
    # Test alert system
    alert_ok = test_alert_system_imports()
    print()
    
    if syntax_ok:
        print("🎉 Backend syntax validation: SUCCESS")
        print("✅ All indentation issues have been fixed")
        print("✅ Main.py can be imported and executed")
    else:
        print("❌ Backend syntax validation: FAILED")
        
    print("=" * 50)
    print("🏥 Hospital Agent Backend Test Complete")