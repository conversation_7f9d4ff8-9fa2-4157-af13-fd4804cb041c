# Application Configuration
APP_NAME="Hospital Operations & Logistics Agentic Platform"
VERSION="1.0.0"
DEBUG=true
HOST="0.0.0.0"
PORT=8000

# Database Configuration
DATABASE_URL="sqlite:///./hospital.db"

# Security Configuration
SECRET_KEY="your-secret-key-change-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_HOSTS="http://localhost:3000,http://127.0.0.1:3000" 

# Redis Configuration
REDIS_URL="redis://localhost:6379"

# Vector Database Configuration
VECTOR_DB_PATH="./data/vector_store"

# LLM Configuration - Google Gemini 2.5 Flash
GOOGLE_API_KEY="AIzaSyCrEfICW4RYyJW45Uy0ZSduXVKUKjNu25I"
LLM_MODEL="gemini-2.5-flash"
LLM_PROVIDER="google"

# MCP Server Configuration
MCP_SERVER_PORT=3001
MCP_SERVER_HOST="localhost"
MCP_ENABLED=true
MCP_LOG_LEVEL="INFO"

# ChromaDB Configuration
CHROMA_PERSIST_DIRECTORY="./data/chroma_db"

# Agent Configuration
MAX_AGENT_RETRIES=3
AGENT_TIMEOUT=30

# Logging Configuration
LOG_LEVEL="INFO"
