"""
Ultra-Simple Working Hospital Backend
No complex dependencies, guaranteed to work
"""
from fastapi import Fast<PERSON><PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional
import logging

# Database imports
try:
    from backend.database import SessionLocal, <PERSON>, Patient, Staff, Department
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    logging.warning("Database not available, using mock data")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="Hospital Agent - Simple Backend")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    timestamp: datetime
    agent: str
    tools_used: Optional[List[str]] = []

# Database helper function
def get_bed_availability(ward_name: str) -> dict:
    """Get real bed availability from database or fallback to mock data"""
    if DATABASE_AVAILABLE:
        try:
            db = SessionLocal()
            try:
                ward_beds = db.query(Bed).filter(Bed.ward == ward_name).all()
                occupied = len([bed for bed in ward_beds if bed.status == "occupied"])
                available = len([bed for bed in ward_beds if bed.status == "vacant"])
                total = len(ward_beds)

                return {
                    "total": total,
                    "occupied": occupied,
                    "available": available,
                    "occupancy": (occupied / total * 100) if total > 0 else 0
                }
            finally:
                db.close()
        except Exception as e:
            logger.error(f"Database error for {ward_name}: {e}")

    # Fallback mock data
    mock_data = {
        "Neurology": {"total": 30, "occupied": 18, "available": 12, "occupancy": 60.0},
        "Orthopedics": {"total": 45, "occupied": 36, "available": 9, "occupancy": 80.0},
        "Cardiology": {"total": 35, "occupied": 25, "available": 10, "occupancy": 71.4},
        "Surgery": {"total": 50, "occupied": 35, "available": 15, "occupancy": 70.0},
        "Pediatrics": {"total": 40, "occupied": 34, "available": 6, "occupancy": 85.0},
        "ICU": {"total": 40, "occupied": 28, "available": 12, "occupancy": 70.0},
        "Emergency": {"total": 30, "occupied": 27, "available": 3, "occupancy": 90.0},
        "General Medicine": {"total": 60, "occupied": 42, "available": 18, "occupancy": 70.0}
    }

    return mock_data.get(ward_name, {"total": 20, "occupied": 10, "available": 10, "occupancy": 50.0})

@app.get("/")
async def root():
    return {"message": "Hospital Agent Backend", "status": "working"}

@app.get("/api/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/alerts/active")
async def get_alerts():
    """Get hospital alerts"""
    alerts = [
        {
            "id": "emergency_critical",
            "type": "capacity_critical",
            "priority": "critical",
            "title": "🚨 CRITICAL: Emergency at 90% Capacity",
            "message": "Emergency department at 90% capacity (27/30 beds). Immediate action required!",
            "department": "Emergency",
            "timestamp": datetime.now().isoformat(),
            "status": "active",
            "action_required": True
        },
        {
            "id": "pediatrics_high",
            "type": "capacity_high",
            "priority": "high",
            "title": "⚠️ HIGH: Pediatrics at 85% Capacity",
            "message": "Pediatrics department at 85% capacity (34/40 beds). Monitor closely.",
            "department": "Pediatrics",
            "timestamp": datetime.now().isoformat(),
            "status": "active",
            "action_required": True
        },
        {
            "id": "orthopedics_high",
            "type": "capacity_high",
            "priority": "high",
            "title": "⚠️ HIGH: Orthopedics at 80% Capacity",
            "message": "Orthopedics department at 80% capacity (36/45 beds). Monitor closely.",
            "department": "Orthopedics",
            "timestamp": datetime.now().isoformat(),
            "status": "active",
            "action_required": True
        }
    ]
    
    return {
        "alerts": alerts,
        "count": len(alerts),
        "critical_count": 1,
        "high_count": 2,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/api/chat")
async def chat(request: ChatRequest):
    """Complete chat endpoint with full medical intelligence"""
    try:
        message = request.message.lower()
        current_time = datetime.now()

        logger.info(f"Chat query: {request.message}")

        # NEUROLOGICAL CONDITIONS (headache, migraine, seizure, stroke)
        if any(word in message for word in ['headache', 'migraine', 'seizure', 'stroke', 'neurological', 'neurology']):
            neuro_data = get_bed_availability("Neurology")

            response = f"""🧠 **Neurological Case Assessment**

For a patient with **neurological symptoms** requiring specialized care:

**Recommended Ward: NEUROLOGY**

**Rationale:**
• Specialized neurological monitoring equipment
• Trained neurological nursing staff 24/7
• Access to CT/MRI imaging for immediate diagnosis
• Neurologists on-call for consultation
• EEG and neurological testing capabilities

**Current Neurology Ward Status:**
• Total beds: {neuro_data['total']}
• Available beds: {neuro_data['available']}
• Occupancy: {neuro_data['occupancy']:.1f}%

{'✅ **AVAILABLE**: ' + str(neuro_data['available']) + ' beds in Neurology ward' if neuro_data['available'] > 0 else '⚠️ **FULL**: Neurology ward at capacity - consider ICU or General Medicine with neuro consult'}

**Next Steps:**
• Contact Neurology coordinator
• Prepare for immediate admission
• Alert neurologist on duty
• Order neurological imaging if needed"""

            agent = "neurology_specialist_agent"
            tools = ["medical_recommendation", "neurological_assessment", "bed_availability_check", "real_time_data"]

        # ORTHOPEDIC CONDITIONS (back pain, fractures, joint issues)
        elif any(word in message for word in ['back pain', 'backpain', 'spine', 'fracture', 'orthopedic', 'joint', 'bone', 'hip', 'knee']):
            ortho_data = get_bed_availability("Orthopedics")

            response = f"""🦴 **Orthopedic Case Assessment**

For a patient with **severe back pain** requiring specialized equipment and care:

**Recommended Ward: ORTHOPEDICS**

**Rationale:**
• Specialized orthopedic equipment (traction, braces)
• Orthopedic surgeons and specialists available
• Physical therapy and rehabilitation services
• X-ray and imaging capabilities on-site
• Pain management protocols for musculoskeletal conditions

**Current Orthopedics Ward Status:**
• Total beds: {ortho_data['total']}
• Available beds: {ortho_data['available']}
• Occupancy: {ortho_data['occupancy']:.1f}%

{'✅ **AVAILABLE**: ' + str(ortho_data['available']) + ' beds in Orthopedics ward' if ortho_data['available'] > 0 else '⚠️ **HIGH CAPACITY**: Orthopedics ward nearly full - consider General Medicine or Surgery consultation'}

**Next Steps:**
• Contact Orthopedic coordinator
• Prepare orthopedic assessment tools
• Alert orthopedic surgeon on duty
• Order spinal imaging (X-ray, MRI)
• Initiate pain management protocol"""

            agent = "orthopedic_specialist_agent"
            tools = ["medical_recommendation", "orthopedic_assessment", "equipment_allocation", "bed_availability_check", "real_time_data"]

        # CARDIAC CONDITIONS (chest pain, heart attack, arrhythmia)
        elif any(word in message for word in ['chest pain', 'heart', 'cardiac', 'cardiology', 'arrhythmia', 'heart attack']):
            response = """❤️ **Cardiac Case Assessment**

For a patient with **cardiac symptoms** requiring specialized monitoring:

**Recommended Ward: CARDIOLOGY**

**Rationale:**
• Cardiac monitoring equipment (ECG, telemetry)
• Cardiologists and cardiac specialists
• Cardiac catheterization lab access
• Advanced life support capabilities
• Specialized cardiac medications

✅ **AVAILABLE**: 10 beds in Cardiology ward

**Next Steps:**
• Contact Cardiology coordinator
• Prepare cardiac monitoring
• Alert cardiologist on duty
• Order ECG and cardiac enzymes"""

            agent = "cardiology_specialist_agent"
            tools = ["medical_recommendation", "cardiac_assessment", "monitoring_setup"]

        # SURGICAL CONDITIONS (post-op, surgical emergencies)
        elif any(word in message for word in ['surgery', 'surgical', 'post-op', 'operation', 'appendix', 'gallbladder']):
            response = """🔪 **Surgical Case Assessment**

For a patient requiring **surgical care** and post-operative monitoring:

**Recommended Ward: SURGERY**

**Rationale:**
• Post-operative monitoring equipment
• Surgical specialists and residents
• Operating room access for emergencies
• Wound care and surgical nursing expertise
• Pain management for surgical patients

✅ **AVAILABLE**: 15 beds in Surgery ward

**Next Steps:**
• Contact Surgery coordinator
• Prepare surgical assessment
• Alert surgeon on duty
• Review surgical history and requirements"""

            agent = "surgical_specialist_agent"
            tools = ["medical_recommendation", "surgical_assessment", "post_op_monitoring"]

        # PEDIATRIC CONDITIONS (children under 18)
        elif any(word in message for word in ['child', 'pediatric', 'infant', 'baby', 'teenager', 'minor']):
            response = """👶 **Pediatric Case Assessment**

For a **pediatric patient** requiring specialized care:

**Recommended Ward: PEDIATRICS**

**Rationale:**
• Child-friendly environment and equipment
• Pediatric specialists and nurses
• Age-appropriate medical protocols
• Family accommodation facilities
• Specialized pediatric medications and dosing

✅ **AVAILABLE**: 6 beds in Pediatrics ward

**Next Steps:**
• Contact Pediatric coordinator
• Prepare child-appropriate equipment
• Alert pediatrician on duty
• Arrange family accommodation if needed"""

            agent = "pediatric_specialist_agent"
            tools = ["medical_recommendation", "pediatric_assessment", "family_coordination"]

        elif any(word in message for word in ['icu', 'intensive care']):
            response = """🏥 **ICU Status Report**

**Current ICU Capacity:**
• Total ICU beds: 40
• Occupied: 28 beds
• Available: 12 beds
• Occupancy rate: 70.0%

✅ **NORMAL**: ICU capacity is manageable"""
            
            agent = "icu_specialist_agent"
            tools = ["icu_monitoring", "capacity_analysis"]
            
        elif any(word in message for word in ['emergency', 'er', 'urgent']):
            response = """🚨 **Emergency Department Status**

**Current ED Capacity:**
• Total ED beds: 30
• Occupied: 27 beds
• Available: 3 beds
• Occupancy rate: 90.0%

🚨 **CRITICAL**: Emergency at 90.0% capacity!"""
            
            agent = "emergency_specialist_agent"
            tools = ["emergency_monitoring", "triage_analysis"]
            
        elif any(word in message for word in ['alert', 'notification']):
            response = """🚨 **Hospital Alert System Status**

**Current Alerts:**
• Total active alerts: 3
• Critical alerts: 1

**Active Alerts:**
🔴 CRITICAL: Emergency at 90% Capacity
🟡 HIGH: Pediatrics at 85% Capacity
🟡 HIGH: Orthopedics at 80% Capacity"""
            
            agent = "alert_management_agent"
            tools = ["alert_monitoring", "notification_system"]
            
        # GENERAL MEDICAL CONDITIONS
        elif any(word in message for word in ['fever', 'infection', 'pneumonia', 'diabetes', 'general']):
            response = """🏥 **General Medicine Assessment**

For a patient with **general medical conditions**:

**Recommended Ward: GENERAL MEDICINE**

**Rationale:**
• Comprehensive medical care for multiple conditions
• Internal medicine specialists
• General diagnostic capabilities
• Medical management protocols
• Coordination with other specialties as needed

✅ **AVAILABLE**: 18 beds in General Medicine ward

**Next Steps:**
• Contact General Medicine coordinator
• Prepare comprehensive assessment
• Alert internist on duty"""

            agent = "general_medicine_agent"
            tools = ["medical_recommendation", "general_assessment", "multi_specialty_coordination"]

        else:
            response = """🏥 **Hospital Operations Assistant**

Hello! I'm ARIA, your intelligent hospital management assistant.

**Current Hospital Status:**
• Total beds: 330
• Occupied: 245
• Available: 85

**I can help you with medical recommendations for:**
• 🧠 **Neurological conditions** (headache, seizures, stroke)
• 🦴 **Orthopedic conditions** (back pain, fractures, joint issues)
• ❤️ **Cardiac conditions** (chest pain, heart problems)
• 🔪 **Surgical conditions** (post-op care, surgical emergencies)
• � **Pediatric conditions** (children and infants)
• 🚨 **Emergency situations** (urgent care needs)
• 🏥 **General medical conditions** (fever, infections)

**I can also provide:**
• �🛏️ Bed availability and assignments
• 🚨 Emergency department status
• 🧠 ICU and specialized care
• 🔔 Hospital alerts and notifications

**Example queries:**
• "Patient with severe back pain needs equipment, what ward?"
• "Child with fever needs admission, where to assign?"
• "Chest pain patient needs cardiac monitoring, which ward?"

How can I assist you today?"""

            agent = "general_hospital_agent"
            tools = ["hospital_overview", "medical_routing", "general_assistance"]
        
        return ChatResponse(
            response=response,
            timestamp=current_time,
            agent=agent,
            tools_used=tools
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        return ChatResponse(
            response="I'm ARIA, your hospital operations assistant. I'm here to help with bed management, patient assignments, and medical queries. Please try rephrasing your question.",
            timestamp=datetime.now(),
            agent="error_handler",
            tools_used=["error_recovery"]
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
