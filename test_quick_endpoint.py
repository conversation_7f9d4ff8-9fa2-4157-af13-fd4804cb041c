"""
Test the quick chat endpoint
"""
import requests
import json

def test_quick_chat():
    print("🚀 TESTING QUICK CHAT ENDPOINT")
    print("=" * 40)
    
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/quick',
            json={
                "message": "if a person has severe backpain, what ward should I assign?"
            },
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Quick Chat Response: {data.get('response', 'No response')[:200]}...")
            print(f"Agent: {data.get('agent', 'Unknown')}")
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_quick_chat()
