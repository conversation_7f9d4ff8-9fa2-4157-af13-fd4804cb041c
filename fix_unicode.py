#!/usr/bin/env python3
"""
Fix Unicode Characters in Backend Files
This script replaces problematic Unicode characters with ASCII equivalents
"""

import os
import re

def fix_unicode_in_file(filepath):
    """Fix unicode characters in a single file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Define replacements
        replacements = {
            '✅': 'SUCCESS:',
            '❌': 'ERROR:',
            '🚨': 'ALERT:',
            '🤖': 'AI:',
            '🏥': 'HOSPITAL:',
            '💬': 'CHAT:',
            '📊': 'ANALYTICS:',
            '💡': 'TIP:',
            '🔄': 'UPDATE:',
            '📋': 'INFO:',
            '🧹': 'CLEANING:',
            '⚠️': 'WARNING:',
            '🔧': 'SYSTEM:',
            '📈': 'METRICS:',
            '🎯': 'TARGET:',
            '⚡': 'EMERGENCY:',
            '🛏️': 'BED:',
            '👨‍⚕️': 'DOCTOR:',
            '🩺': 'MEDICAL:',
            '🏃‍♂️': 'URGENT:',
            '📞': 'CONTACT:',
            '🔍': 'SEARCH:',
            '📝': 'NOTE:',
            '🎉': 'SUCCESS:',
            '🔥': 'CRITICAL:',
            '💯': 'COMPLETE:',
            '🚀': 'LAUNCH:',
            '⭐': 'STAR:',
            '🎊': 'CELEBRATION:',
            '🌟': 'HIGHLIGHT:',
            '💪': 'STRONG:',
            '🎪': 'EVENT:',
            '🎭': 'PERFORMANCE:',
            '🎨': 'DESIGN:',
            '🎵': 'SOUND:',
            '🎶': 'MUSIC:',
            '🎸': 'GUITAR:',
            '🎹': 'PIANO:',
            '🎺': 'TRUMPET:',
            '🎻': 'VIOLIN:',
            '🥁': 'DRUMS:',
            '🎤': 'MIC:',
            '🎧': 'HEADPHONES:',
            '📻': 'RADIO:',
            '📺': 'TV:',
            '📱': 'PHONE:',
            '💻': 'LAPTOP:',
            '🖥️': 'DESKTOP:',
            '⌨️': 'KEYBOARD:',
            '🖱️': 'MOUSE:',
            '🖨️': 'PRINTER:',
            '📷': 'CAMERA:',
            '📹': 'VIDEO:',
            '🔊': 'SPEAKER:',
            '🔇': 'MUTE:',
            '🔈': 'VOLUME_LOW:',
            '🔉': 'VOLUME_MED:',
            '🔊': 'VOLUME_HIGH:',
            '📢': 'ANNOUNCEMENT:',
            '📣': 'MEGAPHONE:',
            '📯': 'HORN:',
            '🔔': 'BELL:',
            '🔕': 'NO_BELL:',
            '🎼': 'MUSIC_SCORE:',
            '🎵': 'MUSICAL_NOTE:',
            '🎶': 'MUSICAL_NOTES:',
            '🎙️': 'STUDIO_MIC:',
            '🎚️': 'LEVEL_SLIDER:',
            '🎛️': 'CONTROL_KNOBS:',
            '🎞️': 'FILM_FRAMES:',
            '📽️': 'FILM_PROJECTOR:',
            '🎬': 'CLAPPER_BOARD:',
            '📺': 'TELEVISION:',
            '📻': 'RADIO:',
            '📱': 'MOBILE_PHONE:',
            '☎️': 'TELEPHONE:',
            '📞': 'TELEPHONE_RECEIVER:',
            '📟': 'PAGER:',
            '📠': 'FAX_MACHINE:',
            '🔋': 'BATTERY:',
            '🔌': 'ELECTRIC_PLUG:',
            '💡': 'LIGHT_BULB:',
            '🔦': 'FLASHLIGHT:',
            '🕯️': 'CANDLE:',
            '🪔': 'DIYA_LAMP:',
            '🏮': 'RED_PAPER_LANTERN:',
            '🪅': 'PINATA:',
            '🎆': 'FIREWORKS:',
            '🎇': 'SPARKLER:',
            '🧨': 'FIRECRACKER:',
            '✨': 'SPARKLES:',
            '🎃': 'JACK_O_LANTERN:',
            '🎄': 'CHRISTMAS_TREE:',
            '🎁': 'WRAPPED_GIFT:',
            '🎀': 'RIBBON:',
            '🎗️': 'REMINDER_RIBBON:',
            '🎟️': 'ADMISSION_TICKETS:',
            '🎫': 'TICKET:',
            '🎖️': 'MILITARY_MEDAL:',
            '🏆': 'TROPHY:',
            '🏅': 'SPORTS_MEDAL:',
            '🥇': 'FIRST_PLACE_MEDAL:',
            '🥈': 'SECOND_PLACE_MEDAL:',
            '🥉': 'THIRD_PLACE_MEDAL:',
            '⚽': 'SOCCER_BALL:',
            '🏀': 'BASKETBALL:',
            '🏈': 'AMERICAN_FOOTBALL:',
            '⚾': 'BASEBALL:',
            '🥎': 'SOFTBALL:',
            '🎾': 'TENNIS:',
            '🏐': 'VOLLEYBALL:',
            '🏉': 'RUGBY_FOOTBALL:',
            '🥏': 'FLYING_DISC:',
            '🎱': 'POOL_8_BALL:',
            '🪀': 'YO_YO:',
            '🏓': 'PING_PONG:',
            '🏸': 'BADMINTON:',
            '🏒': 'ICE_HOCKEY:',
            '🏑': 'FIELD_HOCKEY:',
            '🥍': 'LACROSSE:',
            '🏏': 'CRICKET:',
            '🪃': 'BOOMERANG:',
            '🥅': 'GOAL_NET:',
            '⛳': 'FLAG_IN_HOLE:',
            '🪁': 'KITE:',
            '🏹': 'BOW_AND_ARROW:',
            '🎣': 'FISHING_POLE:',
            '🤿': 'DIVING_MASK:',
            '🥊': 'BOXING_GLOVE:',
            '🥋': 'MARTIAL_ARTS_UNIFORM:',
            '🎽': 'RUNNING_SHIRT:',
            '🛹': 'SKATEBOARD:',
            '🛷': 'SLED:',
            '⛸️': 'ICE_SKATE:',
            '🥌': 'CURLING_STONE:',
            '🎿': 'SKIS:',
            '⛷️': 'SKIER:',
            '🏂': 'SNOWBOARDER:',
            '🪂': 'PARACHUTE:',
            '🏋️‍♀️': 'WOMAN_LIFTING_WEIGHTS:',
            '🏋️‍♂️': 'MAN_LIFTING_WEIGHTS:',
            '🤼‍♀️': 'WOMEN_WRESTLING:',
            '🤼‍♂️': 'MEN_WRESTLING:',
            '🤸‍♀️': 'WOMAN_CARTWHEELING:',
            '🤸‍♂️': 'MAN_CARTWHEELING:',
            '⛹️‍♀️': 'WOMAN_BOUNCING_BALL:',
            '⛹️‍♂️': 'MAN_BOUNCING_BALL:',
            '🤺': 'PERSON_FENCING:',
            '🤾‍♀️': 'WOMAN_PLAYING_HANDBALL:',
            '🤾‍♂️': 'MAN_PLAYING_HANDBALL:',
            '🏌️‍♀️': 'WOMAN_GOLFING:',
            '🏌️‍♂️': 'MAN_GOLFING:',
            '🏇': 'HORSE_RACING:',
            '🧘‍♀️': 'WOMAN_IN_LOTUS_POSITION:',
            '🧘‍♂️': 'MAN_IN_LOTUS_POSITION:',
            '🏄‍♀️': 'WOMAN_SURFING:',
            '🏄‍♂️': 'MAN_SURFING:',
            '🏊‍♀️': 'WOMAN_SWIMMING:',
            '🏊‍♂️': 'MAN_SWIMMING:',
            '🤽‍♀️': 'WOMAN_PLAYING_WATER_POLO:',
            '🤽‍♂️': 'MAN_PLAYING_WATER_POLO:',
            '🚣‍♀️': 'WOMAN_ROWING_BOAT:',
            '🚣‍♂️': 'MAN_ROWING_BOAT:',
            '🧗‍♀️': 'WOMAN_CLIMBING:',
            '🧗‍♂️': 'MAN_CLIMBING:',
            '🚵‍♀️': 'WOMAN_MOUNTAIN_BIKING:',
            '🚵‍♂️': 'MAN_MOUNTAIN_BIKING:',
            '🚴‍♀️': 'WOMAN_BIKING:',
            '🚴‍♂️': 'MAN_BIKING:',
            '🏆': 'TROPHY:',
            '🥇': 'FIRST_PLACE:',
            '🥈': 'SECOND_PLACE:',
            '🥉': 'THIRD_PLACE:',
            '🏅': 'MEDAL:',
            '🎖️': 'MILITARY_MEDAL:',
            '🏵️': 'ROSETTE:',
            '🎗️': 'REMINDER_RIBBON:',
            '🎫': 'TICKET:',
            '🎟️': 'ADMISSION_TICKETS:',
            '🎪': 'CIRCUS_TENT:',
            '🤹‍♀️': 'WOMAN_JUGGLING:',
            '🤹‍♂️': 'MAN_JUGGLING:',
            '🎭': 'PERFORMING_ARTS:',
            '🩰': 'BALLET_SHOES:',
            '🎨': 'ARTIST_PALETTE:',
            '🎬': 'CLAPPER_BOARD:',
            '🎤': 'MICROPHONE:',
            '🎧': 'HEADPHONE:',
            '🎼': 'MUSICAL_SCORE:',
            '🎵': 'MUSICAL_NOTE:',
            '🎶': 'MUSICAL_NOTES:',
            '🪕': 'BANJO:',
            '🥁': 'DRUM:',
            '🪘': 'LONG_DRUM:',
            '🎷': 'SAXOPHONE:',
            '🎺': 'TRUMPET:',
            '🎸': 'GUITAR:',
            '🪗': 'ACCORDION:',
            '🎻': 'VIOLIN:',
            '🪄': 'MAGIC_WAND:',
            '🔮': 'CRYSTAL_BALL:',
            '🪅': 'PINATA:',
            '🪆': 'NESTING_DOLLS:',
            '🖼️': 'FRAMED_PICTURE:',
            '🎨': 'ARTIST_PALETTE:',
            '🧵': 'THREAD:',
            '🪡': 'SEWING_NEEDLE:',
            '🧶': 'YARN:',
            '🪢': 'KNOT:',
        }
        
        # Apply replacements
        modified = False
        for unicode_char, replacement in replacements.items():
            if unicode_char in content:
                content = content.replace(unicode_char, replacement)
                modified = True
                print(f"  Replaced '{unicode_char}' with '{replacement}'")
        
        # Write back if modified
        if modified:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Fixed unicode characters in {filepath}")
            return True
        else:
            print(f"- No unicode characters found in {filepath}")
            return False
            
    except Exception as e:
        print(f"✗ Error processing {filepath}: {e}")
        return False

def main():
    """Main function to fix unicode in all relevant files"""
    files_to_fix = [
        'backend/main.py',
        'backend/enhanced_alert_system.py',
        'agents/bed_management/mcp_agent.py',
        'agents/bed_management/mcp_tools.py'
    ]
    
    print("🔧 Fixing Unicode Characters in Backend Files...")
    print("=" * 60)
    
    fixed_count = 0
    for filepath in files_to_fix:
        if os.path.exists(filepath):
            print(f"\nProcessing: {filepath}")
            if fix_unicode_in_file(filepath):
                fixed_count += 1
        else:
            print(f"✗ File not found: {filepath}")
    
    print("\n" + "=" * 60)
    print(f"🎉 Fixed unicode characters in {fixed_count} files")
    print("✅ Backend should now be stable without unicode encoding errors!")

if __name__ == "__main__":
    main()
