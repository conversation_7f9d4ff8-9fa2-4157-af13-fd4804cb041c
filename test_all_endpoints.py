"""
Comprehensive test of all chatbot endpoints
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_endpoint(endpoint, data, test_name, timeout=15):
    print(f"\n🧪 TESTING: {test_name}")
    print("=" * 60)
    print(f"Endpoint: {endpoint}")
    print(f"Data: {json.dumps(data, indent=2)}")
    
    start_time = time.time()
    try:
        response = requests.post(f"{BASE_URL}{endpoint}", json=data, timeout=timeout)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️ Response Time: {response_time:.2f}s")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ SUCCESS")
            print(f"Agent: {result.get('agent', 'Unknown')}")
            print(f"Response Length: {len(result.get('response', ''))}")
            print(f"Response Preview: {result.get('response', '')[:200]}...")
            return True, response_time, result
        else:
            print(f"❌ FAILED - Status: {response.status_code}")
            print(f"Error: {response.text}")
            return False, response_time, None
            
    except requests.exceptions.Timeout:
        print(f"⏰ TIMEOUT after {timeout}s")
        return False, timeout, None
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False, 0, None

def main():
    print("🏥 COMPREHENSIVE CHATBOT ENDPOINT TESTING")
    print("=" * 80)
    
    # Test scenarios for different endpoints
    test_cases = [
        # Main chat endpoint (should have full LLM/RAG/MCP integration)
        {
            "endpoint": "/api/chat",
            "data": {"message": "Patient with severe chest pain needs immediate ICU bed assignment"},
            "name": "Main Chat - Cardiac Emergency",
            "timeout": 30
        },
        {
            "endpoint": "/api/chat", 
            "data": {"message": "Show me detailed ICU capacity analysis with predictions"},
            "name": "Main Chat - ICU Analysis",
            "timeout": 30
        },
        {
            "endpoint": "/api/chat",
            "data": {"message": "I need bed assignment for orthopedic surgery patient"},
            "name": "Main Chat - Surgery Assignment", 
            "timeout": 30
        },
        
        # Quick chat endpoint (simplified responses)
        {
            "endpoint": "/api/chat/quick",
            "data": {"message": "Patient has back pain, which ward?"},
            "name": "Quick Chat - Back Pain",
            "timeout": 10
        },
        {
            "endpoint": "/api/chat/quick",
            "data": {"message": "Emergency cardiac case needs bed"},
            "name": "Quick Chat - Cardiac",
            "timeout": 10
        },
        
        # Patient assignment endpoint
        {
            "endpoint": "/api/chat/assign-patient",
            "data": {
                "patient_name": "John Doe",
                "bed_id": 1,
                "doctor_id": 1
            },
            "name": "Patient Assignment",
            "timeout": 15
        }
    ]
    
    results = []
    total_time = 0
    
    for test_case in test_cases:
        success, response_time, result = test_endpoint(
            test_case["endpoint"],
            test_case["data"], 
            test_case["name"],
            test_case["timeout"]
        )
        
        results.append({
            "name": test_case["name"],
            "endpoint": test_case["endpoint"],
            "success": success,
            "response_time": response_time,
            "result": result
        })
        
        total_time += response_time
        time.sleep(1)  # Brief pause between tests
    
    # Summary Report
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        time_str = f"{result['response_time']:.2f}s"
        print(f"{status} - {result['name']} ({time_str}) - {result['endpoint']}")
        
        if result["success"]:
            passed += 1
            # Check for enhanced features
            if result["result"]:
                response_text = result["result"].get("response", "").lower()
                agent = result["result"].get("agent", "")
                
                print(f"    🤖 Agent: {agent}")
                
                # Check for enhanced features
                features = []
                if "prediction" in response_text or "forecast" in response_text:
                    features.append("📈 Predictive Analysis")
                if "icu" in response_text and "capacity" in response_text:
                    features.append("🏥 ICU Intelligence")
                if "emergency" in response_text and "protocol" in response_text:
                    features.append("🚨 Emergency Protocols")
                if "department" in response_text and "analysis" in response_text:
                    features.append("📊 Department Analysis")
                if len(response_text) > 500:
                    features.append("📝 Detailed Response")
                
                if features:
                    print(f"    ✨ Features: {', '.join(features)}")
        else:
            failed += 1
    
    print(f"\n📈 OVERALL RESULTS:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"⏱️ Total Time: {total_time:.2f}s")
    print(f"📊 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if failed == 0:
        print("🎉 All endpoints working perfectly!")
    else:
        print("⚠️ Some endpoints need attention:")
        for result in results:
            if not result["success"]:
                print(f"   - Fix {result['endpoint']} ({result['name']})")
    
    # Check for enhanced features
    main_chat_working = any(r["success"] and r["endpoint"] == "/api/chat" for r in results)
    if main_chat_working:
        print("✅ Main chat endpoint with LLM/RAG/MCP integration is working")
    else:
        print("⚠️ Main chat endpoint needs LLM/RAG/MCP integration fixes")

if __name__ == "__main__":
    main()
