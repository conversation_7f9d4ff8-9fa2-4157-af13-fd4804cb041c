"""
Test different chatbot scenarios to ensure it's working properly
"""
import requests
import json

def test_scenario(message, scenario_name):
    print(f"\n🧪 TESTING: {scenario_name}")
    print("=" * 50)
    print(f"Query: {message}")
    
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/quick',
            json={"message": message},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: SUCCESS")
            print(f"Agent: {data.get('agent', 'Unknown')}")
            print(f"Response: {data.get('response', 'No response')[:300]}...")
            return True
        else:
            print(f"❌ Status: ERROR {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🏥 HOSPITAL CHATBOT TESTING")
    print("=" * 60)
    
    scenarios = [
        ("Patient has severe back pain, which ward should I assign?", "Back Pain Case"),
        ("Emergency patient with chest pain needs immediate care", "Cardiac Emergency"),
        ("Show me ICU bed availability", "ICU Status"),
        ("What's the current hospital capacity?", "General Query"),
        ("<PERSON><PERSON> needs orthopedic surgery", "Surgery Case")
    ]
    
    results = []
    for message, name in scenarios:
        success = test_scenario(message, name)
        results.append((name, success))
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY RESULTS:")
    print("=" * 60)
    
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {name}")
    
    total_passed = sum(1 for _, success in results if success)
    print(f"\nOverall: {total_passed}/{len(results)} scenarios passed")
    
    if total_passed == len(results):
        print("🎉 ALL TESTS PASSED! Chatbot is working correctly!")
    else:
        print("⚠️ Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
