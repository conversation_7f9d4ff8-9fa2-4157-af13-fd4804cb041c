#!/usr/bin/env python3
"""
Test Predictive Analytics Endpoints
"""
import requests
import json
import time

def test_predictive_analytics():
    """Test all predictive analytics endpoints"""
    print("🔍 Testing Hospital Agent Predictive Analytics...")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Test 1: Predicted Occupancy
    print("\n📊 Testing Predicted Occupancy Endpoint...")
    try:
        response = requests.get(f"{base_url}/api/beds/predicted-occupancy", timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            curve = data.get('predicted_occupancy_curve', [])
            risk_days = data.get('risk_days', [])
            
            print(f"✅ Prediction Curve: {len(curve)} data points")
            print(f"✅ Risk Periods: {len(risk_days)} high-risk periods")
            print(f"✅ Current Occupancy: {data.get('current_occupancy', 0):.1f}%")
            print(f"✅ Total Beds: {data.get('total_beds', 0)}")
            print(f"✅ Confidence: {data.get('prediction_confidence', 0):.1f}")
            
            # Show sample predictions
            print("\n📈 Sample Predictions:")
            for i, pred in enumerate(curve[:5]):
                time_str = pred.get('time', '')[:16] if pred.get('time') else 'N/A'
                print(f"  {i+1}. {time_str}: {pred.get('occupancy_rate', 0)}% ({pred.get('risk_level', 'unknown')} risk)")
        else:
            print(f"❌ Error: {response.status_code} - {response.text[:200]}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Autonomous Predictions
    print("\n🤖 Testing Autonomous Predictions Endpoint...")
    try:
        response = requests.get(f"{base_url}/api/autonomous/predictions", timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            predictions = data.get('predictions', [])
            bottlenecks = data.get('bottlenecks', [])
            analysis = data.get('capacity_analysis', {})
            
            print(f"✅ Ward Predictions: {len(predictions)} predictions")
            print(f"✅ Bottlenecks: {len(bottlenecks)} identified")
            print(f"✅ High Risk Periods: {analysis.get('high_risk_periods', 0)}")
            print(f"✅ Source: {data.get('source', 'unknown')}")
            
            # Show ward predictions
            print("\n🏥 Ward Predictions:")
            ward_predictions = {}
            for pred in predictions:
                ward = pred.get('ward', 'Unknown')
                if ward not in ward_predictions:
                    ward_predictions[ward] = []
                ward_predictions[ward].append(pred)
            
            for ward, preds in list(ward_predictions.items())[:3]:
                avg_occupancy = sum(p.get('occupancy_rate', 0) for p in preds) / len(preds)
                print(f"  {ward}: {avg_occupancy:.1f}% avg occupancy")
            
            # Show bottlenecks
            print("\n⚠️ Identified Bottlenecks:")
            for bottleneck in bottlenecks[:3]:
                print(f"  {bottleneck.get('ward', 'Unknown')}: {bottleneck.get('issue', 'N/A')}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text[:200]}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 3: Autonomous Status
    print("\n🔧 Testing Autonomous Status Endpoint...")
    try:
        response = requests.get(f"{base_url}/api/autonomous/status", timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            systems = data.get('autonomous_systems', {})
            metrics = data.get('performance_metrics', {})
            alerts = data.get('active_alerts', [])
            
            print(f"✅ System Health: {data.get('system_health', 'unknown')}")
            print(f"✅ Prediction Accuracy: {metrics.get('prediction_accuracy', 0)}%")
            print(f"✅ Active Alerts: {len(alerts)}")
            print(f"✅ Predictions Today: {metrics.get('total_predictions_today', 0)}")
            
            # Show system status
            print("\n🤖 Autonomous Systems:")
            for system_name, system_info in systems.items():
                status = system_info.get('status', 'unknown')
                print(f"  {system_name}: {status}")
            
            # Show active alerts
            print("\n🚨 Active Alerts:")
            for alert in alerts[:3]:
                print(f"  {alert.get('type', 'unknown')}: {alert.get('message', 'N/A')}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text[:200]}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Predictive Analytics Testing Complete!")
    print("\nTo test in the frontend:")
    print("1. Open http://localhost:3000")
    print("2. Navigate to Dashboard")
    print("3. Check the 'Predicted Occupancy (Next 24h)' chart")
    print("4. Click on 'Autonomous Systems' tab")
    print("5. View the '24-Hour Bed Occupancy Predictions' chart")

if __name__ == "__main__":
    test_predictive_analytics()
