"""
Test the enhanced chatbot with LLM/RAG/MCP integration
"""
import requests
import json
import time

def test_enhanced_chat(message, test_name):
    print(f"\n🧪 TESTING: {test_name}")
    print("=" * 60)
    print(f"Query: {message}")
    
    start_time = time.time()
    try:
        response = requests.post(
            'http://localhost:8000/api/chat',
            json={
                "message": message,
                "context": {
                    "isEmergency": "emergency" in message.lower() or "critical" in message.lower(),
                    "isPredictive": "predict" in message.lower() or "forecast" in message.lower(),
                    "timestamp": "2024-01-15T10:30:00Z"
                }
            },
            timeout=30
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️ Response Time: {response_time:.2f}s")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS")
            print(f"🤖 Agent: {data.get('agent', 'Unknown')}")
            print(f"📝 Response Length: {len(data.get('response', ''))}")
            
            # Check for enhanced features
            response_text = data.get('response', '').lower()
            features = []
            
            if 'prediction' in response_text or 'forecast' in response_text:
                features.append("📈 Predictive Analysis")
            if 'icu' in response_text and ('capacity' in response_text or 'occupancy' in response_text):
                features.append("🏥 ICU Intelligence")
            if 'emergency' in response_text and 'protocol' in response_text:
                features.append("🚨 Emergency Protocols")
            if 'department' in response_text and 'analysis' in response_text:
                features.append("📊 Department Analysis")
            if len(data.get('response', '')) > 500:
                features.append("📝 Detailed Response")
            if 'bed' in response_text and 'available' in response_text:
                features.append("🛏️ Bed Management")
            if 'critical' in response_text or 'alert' in response_text:
                features.append("⚠️ Critical Monitoring")
                
            if features:
                print(f"✨ Enhanced Features: {', '.join(features)}")
            
            # Show response preview
            preview = data.get('response', '')[:300]
            print(f"📄 Response Preview:")
            print(f"   {preview}...")
            
            return True, response_time, data
        else:
            print(f"❌ FAILED - Status: {response.status_code}")
            print(f"Error: {response.text}")
            return False, response_time, None
            
    except requests.exceptions.Timeout:
        print(f"⏰ TIMEOUT after 30s")
        return False, 30, None
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False, 0, None

def main():
    print("🏥 ENHANCED CHATBOT TESTING - LLM + RAG + MCP")
    print("=" * 80)
    
    # Advanced test scenarios
    test_scenarios = [
        ("Show me critical ICU status with detailed predictions and capacity analysis", "ICU Intelligence + Predictions"),
        ("Emergency patient with severe chest pain needs immediate bed assignment with cardiac monitoring", "Emergency + Medical Routing"),
        ("Analyze current hospital bottlenecks and provide optimization recommendations", "Department Analysis + Optimization"),
        ("Predict bed availability for next 4 hours and identify potential surge capacity issues", "Predictive Analytics"),
        ("I need to assign a cardiac surgery patient to ICU with specialized equipment", "Complex Bed Assignment"),
        ("What's the current status of all departments with occupancy rates and staff availability?", "Comprehensive Status"),
        ("Emergency surge capacity assessment - how many patients can we handle?", "Surge Planning"),
        ("Show me discharge planning opportunities to free up ICU beds", "Discharge Optimization")
    ]
    
    results = []
    total_time = 0
    enhanced_features_count = 0
    
    for message, name in test_scenarios:
        success, response_time, result = test_enhanced_chat(message, name)
        
        # Count enhanced features
        if result and success:
            response_text = result.get('response', '').lower()
            if any(keyword in response_text for keyword in ['prediction', 'forecast', 'analysis', 'protocol', 'optimization']):
                enhanced_features_count += 1
        
        results.append({
            "name": name,
            "success": success,
            "response_time": response_time,
            "result": result
        })
        
        total_time += response_time
        time.sleep(2)  # Brief pause between tests
    
    # Summary Report
    print("\n" + "=" * 80)
    print("📊 ENHANCED CHATBOT TEST RESULTS")
    print("=" * 80)
    
    passed = sum(1 for r in results if r["success"])
    failed = len(results) - passed
    avg_response_time = total_time / len(results)
    
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        time_str = f"{result['response_time']:.1f}s"
        print(f"{status} - {result['name']} ({time_str})")
        
        if result["success"] and result["result"]:
            agent = result["result"].get("agent", "unknown")
            if "mcp" in agent.lower():
                print(f"    🧠 Enhanced AI: {agent}")
            else:
                print(f"    🤖 Agent: {agent}")
    
    print(f"\n📈 OVERALL PERFORMANCE:")
    print(f"✅ Passed: {passed}/{len(results)} ({(passed/len(results)*100):.1f}%)")
    print(f"❌ Failed: {failed}")
    print(f"⏱️ Average Response Time: {avg_response_time:.1f}s")
    print(f"✨ Enhanced Features Detected: {enhanced_features_count}/{len(results)}")
    
    # Quality Assessment
    print(f"\n🎯 QUALITY ASSESSMENT:")
    if passed == len(results):
        print("🎉 EXCELLENT: All tests passed!")
    elif passed >= len(results) * 0.8:
        print("✅ GOOD: Most tests passed")
    else:
        print("⚠️ NEEDS IMPROVEMENT: Several tests failed")
    
    if avg_response_time < 10:
        print("⚡ FAST: Great response times")
    elif avg_response_time < 20:
        print("⏱️ MODERATE: Acceptable response times")
    else:
        print("🐌 SLOW: Response times need optimization")
    
    if enhanced_features_count >= len(results) * 0.7:
        print("🧠 INTELLIGENT: Strong AI features detected")
    else:
        print("🤖 BASIC: Limited AI enhancement detected")
    
    print(f"\n💡 RECOMMENDATIONS:")
    if failed == 0 and avg_response_time < 15 and enhanced_features_count >= len(results) * 0.8:
        print("🏆 PERFECT: Enhanced chatbot is working excellently!")
        print("✅ LLM + RAG + MCP integration is fully functional")
        print("✅ Ready for production use")
    else:
        if failed > 0:
            print(f"⚠️ Fix {failed} failing endpoints")
        if avg_response_time > 20:
            print("⚠️ Optimize response times")
        if enhanced_features_count < len(results) * 0.5:
            print("⚠️ Enhance AI integration")

if __name__ == "__main__":
    main()
