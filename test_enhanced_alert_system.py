#!/usr/bin/env python3
"""
Test script for Enhanced Alert System
"""
import asyncio
import sys
import os
import requests
import json
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_enhanced_alert_system():
    """Test the enhanced alert system functionality"""
    print("🧪 Testing Enhanced Alert System")
    print("=" * 50)
    
    # Test 1: Import enhanced alert system
    print("\n1️⃣ Testing Enhanced Alert System Import...")
    try:
        from backend.enhanced_alert_system import enhanced_alert_system, AlertType, AlertPriority, AlertStatus, Alert
        from backend.alert_actions import alert_action_handler
        print("✅ Enhanced alert system imported successfully")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Test 2: Initialize alert system
    print("\n2️⃣ Testing Alert System Initialization...")
    try:
        init_success = await enhanced_alert_system.initialize()
        if init_success:
            print("✅ Alert system initialized successfully")
        else:
            print("❌ Alert system initialization failed")
            return False
    except Exception as e:
        print(f"❌ Initialization error: {e}")
        return False
    
    # Test 3: Start monitoring
    print("\n3️⃣ Testing Alert System Monitoring...")
    try:
        monitoring_success = await enhanced_alert_system.start_monitoring()
        if monitoring_success:
            print("✅ Alert monitoring started successfully")
            print(f"📊 Running: {enhanced_alert_system.running}")
            print(f"📊 Monitoring tasks: {len(enhanced_alert_system.monitoring_tasks)}")
        else:
            print("❌ Alert monitoring failed to start")
            return False
    except Exception as e:
        print(f"❌ Monitoring error: {e}")
        return False
    
    # Test 4: Create test alert
    print("\n4️⃣ Testing Alert Creation...")
    try:
        test_alert = Alert(
            id="",
            type=AlertType.CAPACITY_CRITICAL,
            priority=AlertPriority.CRITICAL,
            status=AlertStatus.ACTIVE,
            title="🧪 Test Critical Alert",
            message="This is a test alert for system validation",
            department="Test Department",
            action_required=True,
            metadata={
                "test": True,
                "occupancy_rate": 95.0,
                "total_beds": 20,
                "occupied_beds": 19,
                "available_beds": 1
            }
        )
        
        alert_id = await enhanced_alert_system.create_alert(test_alert)
        if alert_id:
            print(f"✅ Test alert created successfully: {alert_id}")
            print(f"📊 Active alerts: {len(enhanced_alert_system.get_active_alerts())}")
        else:
            print("❌ Failed to create test alert")
            return False
    except Exception as e:
        print(f"❌ Alert creation error: {e}")
        return False
    
    # Test 5: Test alert actions
    print("\n5️⃣ Testing Alert Actions...")
    try:
        # Test expedite discharge action
        result = await alert_action_handler.execute_action(
            action_id="expedite_discharge",
            alert_id=alert_id,
            parameters={},
            executed_by="test_system"
        )
        
        if result["success"]:
            print("✅ Alert action executed successfully")
            print(f"📊 Action result: {result['result']['message']}")
        else:
            print(f"❌ Alert action failed: {result.get('error')}")
            return False
    except Exception as e:
        print(f"❌ Alert action error: {e}")
        return False
    
    # Test 6: Test proactive alerts
    print("\n6️⃣ Testing Proactive Alerts...")
    try:
        await enhanced_alert_system.create_proactive_alerts()
        active_alerts = enhanced_alert_system.get_active_alerts()
        print(f"✅ Proactive alerts created: {len(active_alerts)} total alerts")
        
        # Show alert breakdown
        critical_count = len([a for a in active_alerts if a.get("priority") == "critical"])
        high_count = len([a for a in active_alerts if a.get("priority") == "high"])
        print(f"📊 Critical: {critical_count}, High: {high_count}")
        
    except Exception as e:
        print(f"❌ Proactive alerts error: {e}")
        return False
    
    # Test 7: Test alert resolution
    print("\n7️⃣ Testing Alert Resolution...")
    try:
        await enhanced_alert_system.resolve_alert(alert_id, "test_system", "Test completed")
        remaining_alerts = enhanced_alert_system.get_active_alerts()
        print(f"✅ Test alert resolved successfully")
        print(f"📊 Remaining alerts: {len(remaining_alerts)}")
    except Exception as e:
        print(f"❌ Alert resolution error: {e}")
        return False
    
    # Test 8: Stop monitoring
    print("\n8️⃣ Testing Alert System Shutdown...")
    try:
        await enhanced_alert_system.stop_monitoring()
        print("✅ Alert monitoring stopped successfully")
        print(f"📊 Running: {enhanced_alert_system.running}")
    except Exception as e:
        print(f"❌ Shutdown error: {e}")
        return False
    
    print("\n🎉 All Enhanced Alert System tests passed!")
    return True

def test_backend_endpoints():
    """Test backend API endpoints"""
    print("\n🌐 Testing Backend API Endpoints")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints = [
        ("/api/health", "GET"),
        ("/api/alerts/active", "GET"),
        ("/api/alerts/system/health", "GET"),
        ("/api/beds/occupancy", "GET"),
        ("/api/system/status", "GET")
    ]
    
    results = []
    
    for endpoint, method in endpoints:
        try:
            print(f"\n🔍 Testing {method} {endpoint}")
            
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
            else:
                response = requests.post(f"{base_url}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {endpoint}: {response.status_code} OK")
                
                # Show some response data
                try:
                    data = response.json()
                    if endpoint == "/api/alerts/active":
                        alert_count = len(data.get("alerts", []))
                        print(f"   📊 Active alerts: {alert_count}")
                    elif endpoint == "/api/beds/occupancy":
                        overall = data.get("overall", {})
                        occupancy = overall.get("occupancy_rate", 0)
                        print(f"   📊 Occupancy rate: {occupancy}%")
                    elif endpoint == "/api/system/status":
                        mode = data.get("mode", "unknown")
                        print(f"   📊 System mode: {mode}")
                except:
                    pass
                    
                results.append((endpoint, True, response.status_code))
            else:
                print(f"❌ {endpoint}: {response.status_code} {response.reason}")
                results.append((endpoint, False, response.status_code))
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint}: Connection error - {e}")
            results.append((endpoint, False, "Connection Error"))
        except Exception as e:
            print(f"❌ {endpoint}: Error - {e}")
            results.append((endpoint, False, "Error"))
    
    # Summary
    successful = len([r for r in results if r[1]])
    total = len(results)
    
    print(f"\n📊 API Test Results: {successful}/{total} endpoints working")
    
    if successful == total:
        print("🎉 All API endpoints are working!")
        return True
    else:
        print("⚠️ Some API endpoints have issues")
        for endpoint, success, status in results:
            if not success:
                print(f"   ❌ {endpoint}: {status}")
        return False

def test_alert_actions():
    """Test alert action endpoints"""
    print("\n🎯 Testing Alert Action Endpoints")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # First, try to create a test alert
    try:
        print("\n🔍 Creating test alert...")
        response = requests.post(f"{base_url}/api/alerts/create-test", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                alert_id = data.get("alert_id")
                print(f"✅ Test alert created: {alert_id}")
                
                # Test action execution
                print(f"\n🎯 Testing action execution...")
                action_response = requests.post(
                    f"{base_url}/api/alerts/{alert_id}/execute-action",
                    json={
                        "action_id": "notify_administration",
                        "executed_by": "test_user",
                        "parameters": {}
                    },
                    timeout=10
                )
                
                if action_response.status_code == 200:
                    action_data = action_response.json()
                    if action_data.get("success"):
                        print("✅ Alert action executed successfully")
                        print(f"   📊 Action: {action_data.get('action_id')}")
                        return True
                    else:
                        print(f"❌ Action execution failed: {action_data.get('error')}")
                        return False
                else:
                    print(f"❌ Action endpoint error: {action_response.status_code}")
                    return False
            else:
                print(f"❌ Test alert creation failed: {data.get('error')}")
                return False
        else:
            print(f"❌ Test alert endpoint error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Alert action test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🏥 Hospital Agent - Enhanced Alert System Tests")
    print("=" * 60)
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: Enhanced Alert System
    alert_system_success = await test_enhanced_alert_system()
    
    # Test 2: Backend API Endpoints
    api_success = test_backend_endpoints()
    
    # Test 3: Alert Actions
    actions_success = test_alert_actions()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Enhanced Alert System", alert_system_success),
        ("Backend API Endpoints", api_success),
        ("Alert Actions", actions_success)
    ]
    
    passed = 0
    for test_name, success in tests:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:.<30} {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} test suites passed")
    
    if passed == len(tests):
        print("🎉 ALL TESTS PASSED! Enhanced Alert System is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test execution error: {e}")
        sys.exit(1)