@echo off
echo 🏥 HOSPITAL AGENT - DEMO STARTUP
echo ========================================
echo Starting complete system for mentor demo
echo.

echo 🚀 Step 1: Starting Backend...
echo Backend will run on: http://localhost:8001
echo.

cd /d "C:\Users\<USER>\OneDrive\Desktop\Hospital_Agent"
call hospital_env\Scripts\activate.bat

echo Starting production backend...
start "Hospital Agent Backend" cmd /k "hospital_env\Scripts\python.exe simple_working_backend.py"

echo.
echo ⏳ Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo.
echo 🖥️  Step 2: Starting Frontend...
echo Frontend will run on: http://localhost:3000
echo.

cd frontend
start "Hospital Agent Frontend" cmd /k "npm run dev"

echo.
echo 🎉 DEMO SYSTEM STARTING!
echo ========================================
echo Backend: http://localhost:8001
echo Frontend: http://localhost:3000
echo API Docs: http://localhost:8001/docs
echo.
echo ✅ Both systems are starting in separate windows
echo ✅ Wait 10-15 seconds for full startup
echo ✅ Then open http://localhost:3000 in your browser
echo.
echo Press any key to close this window...
pause > nul
