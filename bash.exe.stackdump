Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8CD0) msys-2.0.dll+0x2118E
0007FFFF9DD0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x69BA
0007FFFF9DD0  0002100469F2 (00021028DF99, 0007FFFF9C88, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9DD0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9DD0  00021006A545 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA0B0  00021006B9A5 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF948B20000 ntdll.dll
7FF9468C0000 KERNEL32.DLL
7FF945F80000 KERNELBASE.dll
7FF9473F0000 USER32.dll
7FF945C70000 win32u.dll
7FF9478A0000 GDI32.dll
7FF946400000 gdi32full.dll
7FF946810000 msvcp_win.dll
7FF9466C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF948580000 advapi32.dll
7FF9475E0000 msvcrt.dll
7FF947D20000 sechost.dll
7FF9471E0000 RPCRT4.dll
7FF945180000 CRYPTBASE.DLL
7FF945EE0000 bcryptPrimitives.dll
7FF947860000 IMM32.DLL
